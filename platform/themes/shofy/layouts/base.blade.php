<!doctype html>
<html {!! Theme::htmlAttributes() !!}>
    <head>
        <meta charset="UTF-8">
        <meta content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5, user-scalable=1" name="viewport" />
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        {!! Theme::partial('header-meta') !!}

        {!! Theme::header() !!}

        {{-- إضافة CSS إصلاح الشريط السفلي --}}
        <link rel="stylesheet" href="{{ Theme::asset()->url('css/mobile-bottom-bar-fix.css') }}?v={{ time() }}">
        <link rel="stylesheet" href="{{ Theme::asset()->url('css/mobile-spacing-fix.css') }}?v={{ time() }}">

        {{-- إضافة CSS تحسين صفحات تسجيل الدخول والتسجيل --}}
        <link rel="stylesheet" href="{{ Theme::asset()->url('css/auth-pages-enhancement.css') }}?v={{ time() }}">

        {{-- إضافة CSS الثيم المحسن --}}
        <link rel="stylesheet" href="{{ Theme::asset()->url('css/enhanced-theme.css') }}?v={{ time() }}">

        {{-- إضافة Font Awesome --}}
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

        {{-- إضافة خط Cairo العربي --}}
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    </head>
    <body {!! Theme::bodyAttributes() !!}>
        {!! apply_filters(THEME_FRONT_BODY, null) !!}

        @yield('content')

        {!! Theme::footer() !!}

        {{-- إضافة JavaScript المحسن --}}
        <script src="{{ Theme::asset()->url('js/enhanced-theme.js') }}?v={{ time() }}"></script>
    </body>
</html>
