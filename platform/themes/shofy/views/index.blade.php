@php
    Theme::set('breadcrumbStyle', 'none');
@endphp

@extends(Theme::getThemeNamespace('layouts.base'))

@section('content')
    {!! apply_filters('theme_front_header_content', null) !!}

    <main>
        <!-- Hero Slider Section -->
        <section class="tp-slider-area">
            <div class="container-fluid p-0">
                <div class="tp-slider-item enhanced-hero-slider" style="height: 500px; display: flex; align-items: center; background: linear-gradient(135deg, #1a365d, #38b2ac);">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="tp-slider-content tp-fade-in">
                                    <span class="tp-slider-subtitle" style="color: rgba(255, 255, 255, 0.9);">خصم يصل إلى 35%</span>
                                    <h3 class="tp-slider-title" style="color: white; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">قطع غيار أصلية لسيارتك</h3>
                                    <p style="color: rgba(255, 255, 255, 0.9);">احصل على أفضل قطع الغيار الأصلية بأسعار منافسة وضمان شامل</p>
                                    <div class="tp-slider-btn-wrapper">
                                        <a href="{{ route('public.products') }}" class="tp-slider-btn enhanced-btn">تسوق الآن</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="tp-slider-thumb text-center">
                                    <img src="{{ Theme::asset()->url('images/slider/car-parts-hero.png') }}" alt="قطع غيار السيارات" style="max-width: 100%; height: auto;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section class="tp-category-area pt-80 pb-50">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="tp-section-title text-center mb-50">
                            <h3 class="tp-section-title-lg">الفئات الرئيسية</h3>
                            <p>تصفح فئات قطع الغيار المختلفة</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    @php
                        $categories = [
                            ['icon' => 'fas fa-cog', 'name' => 'محرك', 'count' => '150'],
                            ['icon' => 'fas fa-stop-circle', 'name' => 'فرامل', 'count' => '89'],
                            ['icon' => 'fas fa-circle', 'name' => 'إطارات', 'count' => '67'],
                            ['icon' => 'fas fa-bolt', 'name' => 'كهرباء', 'count' => '234'],
                            ['icon' => 'fas fa-snowflake', 'name' => 'تكييف', 'count' => '45'],
                            ['icon' => 'fas fa-tint', 'name' => 'زيوت', 'count' => '78']
                        ];
                    @endphp

                    @foreach($categories as $category)
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-30">
                            <div class="tp-category-item enhanced-category-card tp-slide-up">
                                <div class="tp-category-icon">
                                    <i class="{{ $category['icon'] }}"></i>
                                </div>
                                <h4>{{ $category['name'] }}</h4>
                                <span>{{ $category['count'] }} منتج</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>

        <!-- Featured Products Section -->
        <section class="tp-product-area pt-50 pb-80">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="tp-section-title text-center mb-50">
                            <h3 class="tp-section-title-lg">منتجات مميزة</h3>
                            <p>أفضل قطع الغيار المختارة خصيصاً لك</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    @php
                        $products = [
                            ['name' => 'فلتر هواء تويوتا كامري', 'price' => '35.00', 'old_price' => '45.00', 'image' => 'air-filter.jpg'],
                            ['name' => 'بطارية سيارة 12V', 'price' => '280.00', 'old_price' => '320.00', 'image' => 'battery.jpg'],
                            ['name' => 'زيت محرك موبيل 1', 'price' => '85.00', 'old_price' => '95.00', 'image' => 'engine-oil.jpg'],
                            ['name' => 'فرامل سيراميك', 'price' => '450.00', 'old_price' => '500.00', 'image' => 'brake-pads.jpg']
                        ];
                    @endphp

                    @foreach($products as $product)
                        <div class="col-lg-3 col-md-6 col-sm-6 mb-30">
                            <div class="tp-product-item enhanced-product-card tp-fade-in">
                                <div class="tp-product-thumb">
                                    <div class="tp-product-badge">خصم</div>
                                    <img src="https://via.placeholder.com/300x300/38b2ac/ffffff?text={{ urlencode($product['name']) }}" alt="{{ $product['name'] }}">
                                </div>
                                <div class="tp-product-content p-3">
                                    <h4>{{ $product['name'] }}</h4>
                                    <div class="tp-product-price">
                                        <span class="tp-product-price-new">{{ $product['price'] }} ر.س</span>
                                        <span class="tp-product-price-old">{{ $product['old_price'] }} ر.س</span>
                                    </div>
                                    <div class="tp-product-action mt-3">
                                        <button class="tp-btn tp-btn-primary w-100 enhanced-btn">أضف للعربة</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="tp-feature-area pb-80">
            <div class="container">
                <div class="row">
                    @php
                        $features = [
                            ['icon' => 'ti ti-truck-delivery', 'title' => 'توصيل مجاني', 'desc' => 'للطلبات أكثر من 200 ريال'],
                            ['icon' => 'ti ti-currency-dollar', 'title' => 'ضمان الاسترداد', 'desc' => 'ضمان استرداد المال'],
                            ['icon' => 'ti ti-discount-2', 'title' => 'خصومات الأعضاء', 'desc' => 'خصومات حصرية للأعضاء'],
                            ['icon' => 'ti ti-headset', 'title' => 'دعم 24/7', 'desc' => 'خدمة عملاء على مدار الساعة']
                        ];
                    @endphp

                    @foreach($features as $feature)
                        <div class="col-lg-3 col-md-6 col-sm-6 mb-30">
                            <div class="tp-feature-item enhanced-feature-card text-center">
                                <div class="tp-feature-icon">
                                    <i class="{{ $feature['icon'] }}"></i>
                                </div>
                                <div class="tp-feature-content">
                                    <h4>{{ $feature['title'] }}</h4>
                                    <p>{{ $feature['desc'] }}</p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    </main>

    {!! apply_filters('theme_front_footer_content', null) !!}
@endsection
