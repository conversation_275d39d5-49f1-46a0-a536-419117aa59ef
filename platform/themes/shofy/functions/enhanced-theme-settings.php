<?php

use Bo<PERSON>ble\Theme\Facades\Theme;

// Enhanced theme settings and configurations
if (!function_exists('enhanced_theme_setup')) {
    function enhanced_theme_setup()
    {
        // Set default theme options for enhanced theme
        $defaultOptions = [
            'site_title' => 'دليل قطع غيار السيارات',
            'site_description' => 'متجرك الموثوق لجميع قطع غيار السيارات الأصلية',
            'header_style' => '1',
            'logo_height' => '40',
            'primary_color' => '#1a365d',
            'secondary_color' => '#38b2ac',
            'accent_color' => '#ed8936',
            'header_main_background_color' => 'linear-gradient(135deg, #1a365d, #38b2ac)',
            'header_main_text_color' => '#ffffff',
            'header_menu_background_color' => '#ffffff',
            'header_menu_text_color' => '#1a365d',
            'footer_background_color' => 'linear-gradient(135deg, #1a365d, #0f2a44)',
            'footer_text_color' => '#ffffff',
            'hotline' => '8 800 332 65-66',
        ];

        foreach ($defaultOptions as $key => $value) {
            if (!theme_option($key)) {
                Theme::setOption($key, $value);
            }
        }
    }
}

// Enhanced product card rendering
if (!function_exists('render_enhanced_product_card')) {
    function render_enhanced_product_card($product, $classes = '')
    {
        $html = '<div class="tp-product-item enhanced-product-card ' . $classes . '">';
        $html .= '<div class="tp-product-thumb">';
        
        if ($product->sale_price && $product->sale_price < $product->price) {
            $html .= '<div class="tp-product-badge">خصم</div>';
        }
        
        $html .= '<img src="' . ($product->image_url ?: 'https://via.placeholder.com/300x300/38b2ac/ffffff?text=' . urlencode($product->name)) . '" alt="' . $product->name . '">';
        $html .= '</div>';
        
        $html .= '<div class="tp-product-content p-3">';
        $html .= '<h4>' . $product->name . '</h4>';
        $html .= '<div class="tp-product-price">';
        
        if ($product->sale_price && $product->sale_price < $product->price) {
            $html .= '<span class="tp-product-price-new">' . format_price($product->sale_price) . '</span>';
            $html .= '<span class="tp-product-price-old">' . format_price($product->price) . '</span>';
        } else {
            $html .= '<span class="tp-product-price-new">' . format_price($product->price) . '</span>';
        }
        
        $html .= '</div>';
        $html .= '<div class="tp-product-action mt-3">';
        $html .= '<button class="tp-btn tp-btn-primary w-100 enhanced-btn" data-product-id="' . $product->id . '">أضف للعربة</button>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
}

// Enhanced category card rendering
if (!function_exists('render_enhanced_category_card')) {
    function render_enhanced_category_card($category, $icon = 'fas fa-cog', $classes = '')
    {
        $productCount = $category->products_count ?? 0;
        
        $html = '<div class="enhanced-category-card ' . $classes . '">';
        $html .= '<div class="tp-category-icon">';
        $html .= '<i class="' . $icon . '"></i>';
        $html .= '</div>';
        $html .= '<h4>' . $category->name . '</h4>';
        $html .= '<span>' . $productCount . ' منتج</span>';
        $html .= '</div>';
        
        return $html;
    }
}

// Enhanced feature card rendering
if (!function_exists('render_enhanced_feature_card')) {
    function render_enhanced_feature_card($icon, $title, $description, $classes = '')
    {
        $html = '<div class="enhanced-feature-card text-center ' . $classes . '">';
        $html .= '<div class="tp-feature-icon">';
        $html .= '<i class="' . $icon . '"></i>';
        $html .= '</div>';
        $html .= '<div class="tp-feature-content">';
        $html .= '<h4>' . $title . '</h4>';
        $html .= '<p>' . $description . '</p>';
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
}

// Enhanced breadcrumb
if (!function_exists('enhanced_breadcrumb')) {
    function enhanced_breadcrumb($items = [])
    {
        if (empty($items)) {
            $items = [
                ['name' => 'الرئيسية', 'url' => url('/')],
                ['name' => 'الصفحة الحالية', 'url' => null]
            ];
        }
        
        $html = '<nav aria-label="breadcrumb">';
        $html .= '<ol class="breadcrumb">';
        
        foreach ($items as $index => $item) {
            $isLast = $index === count($items) - 1;
            
            if ($isLast) {
                $html .= '<li class="breadcrumb-item active" aria-current="page">' . $item['name'] . '</li>';
            } else {
                $html .= '<li class="breadcrumb-item"><a href="' . $item['url'] . '">' . $item['name'] . '</a></li>';
            }
        }
        
        $html .= '</ol>';
        $html .= '</nav>';
        
        return $html;
    }
}

// Enhanced search suggestions
if (!function_exists('get_search_suggestions')) {
    function get_search_suggestions($query, $limit = 5)
    {
        // This would typically query your database
        $suggestions = [
            'فلتر هواء',
            'زيت محرك',
            'فرامل',
            'بطارية',
            'إطارات'
        ];
        
        $filtered = array_filter($suggestions, function($suggestion) use ($query) {
            return stripos($suggestion, $query) !== false;
        });
        
        return array_slice($filtered, 0, $limit);
    }
}

// Enhanced meta tags
if (!function_exists('enhanced_meta_tags')) {
    function enhanced_meta_tags()
    {
        $html = '<meta name="theme-color" content="#1a365d">';
        $html .= '<meta name="apple-mobile-web-app-capable" content="yes">';
        $html .= '<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">';
        $html .= '<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0">';
        
        return $html;
    }
}

// Initialize enhanced theme
add_action('init', 'enhanced_theme_setup');

// Add enhanced meta tags to head
add_action('theme_front_header', function() {
    echo enhanced_meta_tags();
});

// Enhanced theme customization hooks
add_filter('theme_front_header_content', function($content) {
    // Add any additional header content here
    return $content;
});

add_filter('theme_front_footer_content', function($content) {
    // Add any additional footer content here
    return $content;
});

// Enhanced product filtering
add_filter('ecommerce_products_filter', function($products, $request) {
    // Add custom filtering logic here
    return $products;
}, 10, 2);

// Enhanced search results
add_filter('ecommerce_search_products', function($products, $query) {
    // Add custom search logic here
    return $products;
}, 10, 2);
